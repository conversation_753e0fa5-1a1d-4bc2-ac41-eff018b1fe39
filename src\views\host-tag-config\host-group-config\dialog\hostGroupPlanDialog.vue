<template>
  <!-- 主机组规划对话框 -->
  <el-dialog
    :title="dialogTitle"
    :visible.sync="visible"
    width="1000px"
    :close-on-click-modal="false"
    :show-close="true"
    @close="$emit('close')"
    append-to-body
  >
    <el-form
      :model="form"
      :rules="rules"
      ref="form"
      label-width="120px"
      label-position="left"
    >
      <!-- 基础配置 -->
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="LVS模式" prop="lvs_mode">
            <el-select
              v-model="form.lvs_mode"
              placeholder="请选择LVS模式"
              style="width: 100%"
              @change="onLvsModeChange"
            >
              <el-option label="普通模式" :value="1"></el-option>
              <el-option label="隧道模式" :value="2"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="资源层级" prop="level">
            <el-select
              v-model="form.level"
              placeholder="请选择资源层级"
              style="width: 100%"
            >
              <el-option label="边缘" :value="1"></el-option>
              <el-option label="一层父" :value="2"></el-option>
              <el-option label="二层父" :value="3"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <!-- IPv6配置 - 当lvs_mode=1时显示 -->
      <el-row :gutter="20" v-if="form.lvs_mode === 1">
        <el-col :span="12">
          <el-form-item label="启用IPv6" prop="is_ipv6_enable">
            <el-switch
              v-model="form.is_ipv6_enable"
              :active-value="1"
              :inactive-value="0"
            ></el-switch>
          </el-form-item>
        </el-col>
      </el-row>

      <!-- VIP配置 - 当lvs_mode=2时显示 -->
      <el-row :gutter="20" v-if="form.lvs_mode === 2">
        <el-col :span="12">
          <el-form-item label="IPv4 VIP个数" prop="v4_vip_num">
            <el-input-number
              v-model="form.v4_vip_num"
              :min="0"
              style="width: 100%"
              placeholder="请输入IPv4 VIP个数"
            ></el-input-number>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="IPv6 VIP个数" prop="v6_vip_num">
            <el-input-number
              v-model="form.v6_vip_num"
              :min="0"
              style="width: 100%"
              placeholder="请输入IPv6 VIP个数"
            ></el-input-number>
          </el-form-item>
        </el-col>
      </el-row>

      <!-- LVS组配置 - 当lvs_mode=2时必填 -->
      <el-row :gutter="20" v-if="form.lvs_mode === 2">
        <el-col :span="12">
          <el-form-item label="LVS组" prop="lvs_group">
            <el-select
              v-model="form.lvs_group"
              placeholder="请输入LVS组"
              style="width: 100%"
              filterable
            >
              <el-option v-for="item in lvsGroupOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
          </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <!-- 运营商编码配置 - 仅在隧道模式时显示 -->
      <el-row :gutter="20" v-if="form.lvs_mode === 2">
        <el-col :span="12">
          <el-form-item label="运营商编码" prop="isp_code">
            <el-select
              v-model="form.isp_code"
              placeholder="请选择运营商编码"
              style="width: 100%"
              filterable
            >
              <el-option
                v-for="isp in ispList"
                :key="isp.code"
                :label="`${isp.cn_name} (${isp.code})`"
                :value="isp.code"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <!-- 其他配置字段 -->
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="资源分组" prop="resource_group">
            <el-select
              v-model="form.resource_group"
              placeholder="请选择资源分组"
              style="width: 100%"
              filterable
            >
              <el-option
                v-for="group in resourceGroupOptions"
                :key="group.value"
                :label="group.label"
                :value="group.value"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="端口" prop="port">
            <el-input
              v-model="form.port"
              style="width: 100%"
              placeholder="请输入端口号，多个端口用英文逗号分割"
            ></el-input>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="标签" prop="rap_tag_infos">
            <el-select
              v-model="form.rap_tag_infos"
              placeholder="请选择标签"
              style="width: 100%"
              filterable
              multiple
            >
              <el-option
                v-for="tag in bizLabelOptions"
                :key="tag.value"
                :label="tag.label"
                :value="tag.value"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <!-- 备注 -->
      <el-row>
        <el-col :span="24">
          <el-form-item label="备注" prop="remark">
            <el-input
              v-model="form.remark"
              type="textarea"
              :rows="3"
              placeholder="请输入备注"
              style="width: 100%"
            ></el-input>
          </el-form-item>
        </el-col>
      </el-row>

      <!-- 主机标签选择区域 -->
      <el-divider content-position="left">主机标签选择</el-divider>
      <!-- 主机标签查询条件 -->
      <el-form :model="hostTagSearchForm" inline label-width="80px">
        <el-form-item label="IP地址">
          <el-input
            v-model="hostTagSearchForm.ip"
            placeholder="请输入IP地址"
            style="width: 200px"
            clearable
          ></el-input>
        </el-form-item>
        <el-form-item label="标签键">
          <el-input
            v-model="hostTagSearchForm.tag_key"
            placeholder="请输入标签键"
            style="width: 200px"
            clearable
          ></el-input>
        </el-form-item>
        <el-form-item label="标签值">
          <el-input
            v-model="hostTagSearchForm.tag_value"
            placeholder="请输入标签值"
            style="width: 200px"
            clearable
          ></el-input>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="searchHostTags">查询</el-button>
          <el-button @click="resetHostTagSearch">重置</el-button>
        </el-form-item>
      </el-form>

      <!-- 主机标签数据表格 -->
      <el-table
        ref="hostTagTable"
        :data="hostTagData"
        v-loading="hostTagLoading"
        border
        @selection-change="handleHostTagSelectionChange"
        style="width: 100%; margin-bottom: 20px"
        max-height="300"
      >
        <el-table-column
          type="selection"
          width="55"
          align="center"
          :selectable="isRowSelectable"
        ></el-table-column>
        <el-table-column label="IP地址" align="center" width="150">
          <template slot-scope="scope">
            <span
              :class="{ 'ip-already-added': form.ip_list.includes(scope.row.ip) }"
              :title="form.ip_list.includes(scope.row.ip) ? '该IP已添加到当前记录' : ''"
            >
              {{ scope.row.ip }}
            </span>
            <el-tag
              v-if="form.ip_list.includes(scope.row.ip)"
              size="mini"
              type="success"
              style="margin-left: 5px;"
            >
              已添加
            </el-tag>
          </template>
        </el-table-column>

        <!-- 标签信息列 - 处理嵌套的tag_infos数组 -->
        <el-table-column label="标签信息" align="left" min-width="300">
          <template slot-scope="scope">
            <div v-if="scope.row.tag_infos && scope.row.tag_infos.length > 0" class="tag-info-container">
              <!-- 显示前3个标签 -->
              <el-tag
                v-for="(tag, index) in scope.row.tag_infos.slice(0, 3)"
                :key="index"
                size="small"
                :title="`${tag.tag_key}: ${tag.tag_value}`"
              >
                {{ tag.tag_key }}: {{ tag.tag_value }}
              </el-tag>

              <!-- 如果有超过3个标签，显示省略提示 -->
              <el-tooltip v-if="scope.row.tag_infos.length > 3" placement="top" class="tag-tooltip-content">
                <div slot="content">
                  <div v-for="(tag, index) in scope.row.tag_infos.slice(3)" :key="index" style="margin: 2px 0;">
                    <strong>{{ tag.tag_key }}:</strong> {{ tag.tag_value }}
                  </div>
                </div>
                <el-tag size="small" type="info">
                  +{{ scope.row.tag_infos.length - 3 }}个标签
                </el-tag>
              </el-tooltip>
            </div>
            <span v-else style="color: #999; font-style: italic;">无标签信息</span>
          </template>
        </el-table-column>

        <el-table-column prop="cdn_type" label="CDN类型" align="center" width="100"></el-table-column>
        <el-table-column prop="module" label="模块" align="center" width="150"></el-table-column>
      </el-table>

      <!-- 添加选中IP按钮 -->
      <el-row style="margin-bottom: 20px">
        <el-button
          type="success"
          @click="addSelectedIPs"
          :disabled="selectedHostTags.length === 0"
        >
          添加选中IP到当前记录 ({{ selectedHostTags.length }})
        </el-button>
      </el-row>

      <!-- 当前记录的IP列表 -->
      <el-form-item label="IP列表" prop="ip_list">
        <el-tag
          v-for="(ip, index) in form.ip_list"
          :key="index"
          closable
          @close="removeIP(index)"
          style="margin-right: 10px; margin-bottom: 5px"
        >
          {{ ip }}
        </el-tag>
        <el-input
          v-if="inputVisible"
          v-model="inputValue"
          ref="saveTagInput"
          size="small"
          @keyup.enter.native="handleInputConfirm"
          @blur="handleInputConfirm"
          style="width: 150px"
        ></el-input>
        <el-button v-else size="small" @click="showInput">+ 手动添加IP</el-button>
      </el-form-item>
    </el-form>

    <!-- 对话框底部按钮 -->
    <div slot="footer" class="dialog-footer">
      <el-button @click="$emit('close')" :disabled="submitting || checking">取消</el-button>
      <el-button type="primary" @click="handleSubmit" :loading="submitting || checking">
        <span v-if="checking">校验中...</span>
        <span v-else-if="submitting">{{ isEdit ? '修改中...' : '新增中...' }}</span>
        <span v-else>{{ isEdit ? '修改' : '新增' }}</span>
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
import http from "@/views/host-tag-config/http.js";

export default {
  name: "host-group-plan-dialog",
  props: {
    // 对话框显示状态
    visible: {
      type: Boolean,
      default: false
    },
    // 是否为编辑模式
    isEdit: {
      type: Boolean,
      default: false
    },
    // 当前编辑的行数据
    currentRowData: {
      type: Object,
      default: () => ({})
    },
  },
  data() {
    return {
      submitting: false,
      checking: false, // 校验中状态

      // 主表单数据
      form: {
        v4_vip_num: 0,
        v6_vip_num: 0,
        is_ipv6_enable: 0,
        lvs_mode: 1,
        lvs_group: "",
        isp_code: "",
        level: 1,
        resource_group: "",
        port: "",
        rap_tag_infos: [],
        ip_list: [],
        remark: "",
      },

      // 表单验证规则
      rules: {
        lvs_mode: [
          { required: true, message: "请选择LVS模式", trigger: "change" }
        ],
        level: [
          { required: true, message: "请选择资源层级", trigger: "change" }
        ],
        isp_code: [
          {
            validator: (rule, value, callback) => {
              if (this.form.lvs_mode === 2 && !value) {
                callback(new Error("隧道模式时必须选择运营商编码"));
              } else {
                callback();
              }
            },
            trigger: "change"
          }
        ],
        resource_group: [
          { required: true, message: "请选择资源分组", trigger: "change" }
        ],
        port: [
          { required: true, message: "请输入端口号", trigger: "blur" },
          {
            validator: (rule, value, callback) => {
              if (!value || value.trim() === "") {
                callback();
                return;
              }

              // 支持多个端口，用逗号分割
              const ports = value.split(',').map(p => p.trim()).filter(p => p !== '');

              for (const port of ports) {
                // 检查是否为数字
                if (!/^\d+$/.test(port)) {
                  callback(new Error("端口号必须为数字"));
                  return;
                }

                const portNum = parseInt(port);
                if (portNum < 1 || portNum > 65535) {
                  callback(new Error("端口号必须在1-65535之间"));
                  return;
                }
              }

              if (new Set(ports).size !== ports.length) callback(new Error("端口号重复"))

              callback();
            },
            trigger: "blur"
          }
        ],
        lvs_group: [
          {
            validator: (rule, value, callback) => {
              if (this.form.lvs_mode === 2 && !value) {
                callback(new Error("隧道模式时必须配置LVS组"));
              } else {
                callback();
              }
            },
            trigger: "blur"
          }
        ],
        v4_vip_num: [
          {
            validator: (rule, value, callback) => {
              if (this.form.lvs_mode === 2 && (value === null || value === undefined)) {
                callback(new Error("隧道模式时必须配置IPv4 VIP个数"));
              } else {
                callback();
              }
            },
            trigger: "blur"
          }
        ],
        v6_vip_num: [
          {
            validator: (rule, value, callback) => {
              if (this.form.lvs_mode === 2 && (value === null || value === undefined)) {
                callback(new Error("隧道模式时必须配置IPv6 VIP个数"));
              } else {
                callback();
              }
            },
            trigger: "blur"
          }
        ],
      },

      // 主机标签查询表单
      hostTagSearchForm: {
        ip: "",
        tag_key: "",
        tag_value: "",
      },

      // 主机标签数据
      hostTagData: [],
      hostTagLoading: false,
      selectedHostTags: [],

      // 手动添加IP相关
      inputVisible: false,
      inputValue: "",

      // 选项列表
      lvsGroupOptions: [],
      ispList: [],
      resourceGroupOptions: [],
      bizLabelOptions: []
    };
  },
  computed: {
    // 对话框标题
    dialogTitle() {
      return this.isEdit ? '修改主机组规划' : '新增主机组规划';
    },
  },
  watch: {
    // 监听对话框显示状态，显示时初始化表单
    visible(val) {
      if (val) {
        this.initForm();
        this.searchHostTags(); // 默认加载主机标签数据
        this.loadLvsOptions();
        this.loadIspList();
        this.loadResourceGroupOptions();
        this.loadBizLabelOptions();
      }
    }
  },
  methods: {
    // 初始化表单
    initForm() {
      if (this.isEdit && this.currentRowData) {
        // 编辑模式：填充现有数据
        this.form = {
          ...this.currentRowData,
          rap_tag_infos: (this.currentRowData.rap_tag_infos || "").split(",").filter(Boolean),
          ip_list: Array.isArray(this.currentRowData.ip_list)
            ? [...this.currentRowData.ip_list]
            : (this.currentRowData.ip_list ? this.currentRowData.ip_list.split(',') : [])
        };
      } else {
        // 新增模式：重置表单
        this.form = {
          v4_vip_num: 0,
          v6_vip_num: 0,
          is_ipv6_enable: 0,
          lvs_mode: 1,
          lvs_group: "",
          isp_code: "",
          level: 1,
          resource_group: "",
          port: "",
          rap_tag_infos: [],
          ip_list: [],
          remark: "",
        };
      }

      // 重置验证状态
      this.$nextTick(() => {
        if (this.$refs.form) {
          this.$refs.form.clearValidate();
        }
      });
    },

    async loadLvsOptions() {
      try {
        const res = await http.getLvsGroupOptions();
        if (res) {
          this.lvsGroupOptions = res?.map(itm => ({
            label: itm.group_name,
            value: itm.group_name
          })) || [];
        }
      } catch (error) {
        console.error('加载LVS组选项失败:', error);
      }
    },

    // 加载运营商编码列表
    async loadIspList() {
      try {
        const res = await http.getIspList();
        if (res && res.code === 100000) {
          this.ispList = res.data || [];
        }
      } catch (error) {
        console.error('加载运营商编码列表失败:', error);
      }
    },

    // 加载资源分组选项
    async loadResourceGroupOptions() {
      try {
        const res = await http.getResourceGroupOptions();
        if (res) {
          this.resourceGroupOptions = res?.map(itm => ({
            label: itm.bk_inst_name,
            biz_type: itm.biz_type,
            value: `${itm.group_id}`
          })) || [];
        }
      } catch (error) {
        console.error('加载资源分组选项失败:', error);
      }
    },

    // 加载业务标签选项
    async loadBizLabelOptions() {
      try {
        const res = await http.getBizLabelOptions();
        if (res) {
          this.bizLabelOptions = res?.map(itm => ({
            label: itm.tagCnName,
            value: itm.tagCode
          })) || [];
        }
      } catch (error) {
        console.error('加载业务标签选项失败:', error);
      }
    },

    // LVS模式变化处理
    onLvsModeChange(value) {
      // 清除相关字段的验证错误
      this.$nextTick(() => {
        if (this.$refs.form) {
          this.$refs.form.clearValidate(['lvs_group', 'v4_vip_num', 'v6_vip_num', 'isp_code']);
        }
      });

      // 根据模式重置相关字段
      if (value === 1) {
        // 普通模式：清空VIP配置、LVS组和运营商编码
        this.form.v4_vip_num = 0;
        this.form.v6_vip_num = 0;
        this.form.lvs_group = "";
        this.form.isp_code = "";
      } else if (value === 2) {
        // 隧道模式：重置IPv6启用状态
        this.form.is_ipv6_enable = 0;
      }
    },

    // 查询主机标签数据
    async searchHostTags() {
      this.hostTagLoading = true;
      try {
        const params = {
          ...this.hostTagSearchForm,
          page: 1,
          page_size: 100000, // 限制返回数量
        };

        const response = await http.getHostTagList(params);
        if (response) {
          let hostTagData = response.data?.items || [];

          // 根据标签键和标签值进行客户端筛选
          if (this.hostTagSearchForm.tag_key || this.hostTagSearchForm.tag_value) {
            hostTagData = hostTagData.filter(item => {
              return this.isTagMatched(
                item.tag_infos,
                this.hostTagSearchForm.tag_key,
                this.hostTagSearchForm.tag_value
              );
            });
          }

          this.hostTagData = hostTagData;
        } else {
          this.hostTagData = [];
        }
      } catch (error) {
        console.error("查询主机标签数据失败:", error);
        this.hostTagData = [];
        this.$message.error("查询主机标签失败");
      } finally {
        this.hostTagLoading = false;
      }
    },

    // 重置主机标签查询条件
    resetHostTagSearch() {
      this.hostTagSearchForm = {
        ip: "",
        tag_key: "",
        tag_value: "",
      };
      this.searchHostTags();
    },

    // 主机标签选择变化
    handleHostTagSelectionChange(selection) {
      this.selectedHostTags = selection;
    },

    // 添加选中的IP到当前记录
    addSelectedIPs() {
      const newIPs = this.selectedHostTags
        .map(item => item.ip)
        .filter(ip => !this.form.ip_list.includes(ip)); // 去重

      if (newIPs.length > 0) {
        this.form.ip_list = [...this.form.ip_list, ...newIPs];
        this.$message.success(`成功添加 ${newIPs.length} 个IP地址`);

        // 添加成功后清空表格选中状态
        this.$nextTick(() => {
          if (this.$refs.hostTagTable) {
            this.$refs.hostTagTable.clearSelection();
          }
        });
      } else {
        this.$message.warning("所选IP地址已存在于当前记录中");
      }
    },

    // 移除IP
    removeIP(index) {
      this.form.ip_list.splice(index, 1);

      // 移除IP后，更新表格的选择状态（被移除的IP对应的行现在可以被选择了）
      // 这里不需要特殊处理，因为isRowSelectable方法会自动重新计算
    },

    // 显示手动添加IP输入框
    showInput() {
      this.inputVisible = true;
      this.$nextTick(() => {
        this.$refs.saveTagInput.$refs.input.focus();
      });
    },

    // 确认手动添加IP
    handleInputConfirm() {
      const inputValue = this.inputValue.trim();
      if (inputValue && !this.form.ip_list.includes(inputValue)) {
        // 简单的IP格式验证
        const ipRegex = /^(\d{1,3}\.){3}\d{1,3}$/;
        if (ipRegex.test(inputValue)) {
          this.form.ip_list.push(inputValue);
        } else {
          this.$message.warning("请输入有效的IP地址格式");
        }
      }
      this.inputVisible = false;
      this.inputValue = "";
    },

    // 检查行是否可选择（控制checkbox的禁用状态）
    isRowSelectable(row) {
      // 如果该IP已经存在于当前记录的ip_list中，则不可选择
      return !this.form.ip_list.includes(row.ip);
    },

    // 格式化标签信息显示
    formatTagInfo(tagInfos) {
      if (!tagInfos || !Array.isArray(tagInfos) || tagInfos.length === 0) {
        return "无标签信息";
      }

      return tagInfos.map(tag => `${tag.tag_key}: ${tag.tag_value}`).join(", ");
    },

    // 检查标签是否匹配搜索条件
    isTagMatched(tagInfos, searchKey, searchValue) {
      if (!tagInfos || !Array.isArray(tagInfos)) {
        return false;
      }

      return tagInfos.some(tag => {
        const keyMatch = !searchKey || tag.tag_key.toLowerCase().includes(searchKey.toLowerCase());
        const valueMatch = !searchValue || tag.tag_value.toLowerCase().includes(searchValue.toLowerCase());
        return keyMatch && valueMatch;
      });
    },

    // 获取标签的简短显示文本
    getTagDisplayText(tagInfos, maxLength = 50) {
      const fullText = this.formatTagInfo(tagInfos);
      if (fullText.length <= maxLength) {
        return fullText;
      }
      return fullText.substring(0, maxLength) + "...";
    },

    // 校验主机组规划
    async checkHostGroupPlan() {
      if (!this.form.ip_list || this.form.ip_list.length === 0) {
        return true; // 如果没有IP列表，跳过校验
      }

      this.checking = true;
      try {
        const checkData = {
          ip_list: this.form.ip_list
        };

        const response = await http.checkHostGroupPlan(checkData);
        if (response?.data?.code === 100000) {
          // 校验通过
          return true;
        } else {
          // 校验失败，显示错误信息并询问用户是否继续
          const errorMessage = response?.data?.message || "校验失败，存在未知错误";

          const confirmResult = await this.$confirm(
            `校验发现以下问题：\n${errorMessage}\n\n是否要忽略校验错误继续提交？\n注意：忽略校验错误可能导致配置异常。`,
            '校验失败',
            {
              confirmButtonText: '继续提交',
              cancelButtonText: '取消',
              type: 'warning',
              dangerouslyUseHTMLString: false
            }
          ).catch(() => false);

          return confirmResult;
        }
      } catch (error) {
        console.error("校验主机组规划失败:", error);

        // 网络错误或其他异常，询问用户是否继续
        const confirmResult = await this.$confirm(
          `校验接口调用失败：${error.message || '网络错误'}\n\n是否要跳过校验继续提交？\n注意：跳过校验可能导致配置异常。`,
          '校验异常',
          {
            confirmButtonText: '继续提交',
            cancelButtonText: '取消',
            type: 'error',
            dangerouslyUseHTMLString: false
          }
        ).catch(() => false);

        return confirmResult;
      } finally {
        this.checking = false;
      }
    },

    // 提交表单
    async handleSubmit() {
      // 表单验证
      const valid = await this.$refs.form.validate().catch(() => false);
      if (!valid) {
        return;
      }

      // 检查IP列表
      if (!this.form.ip_list || this.form.ip_list.length === 0) {
        this.$message.warning("请至少添加一个IP地址");
        return;
      }

      // 提交前校验
      const checkPassed = await this.checkHostGroupPlan();
      if (!checkPassed) {
        return;
      }

      this.submitting = true;
      try {
        // 获取操作人信息
        const operator = window.localStorage.getItem("userInfo") || "system";

        const submitData = {
          ...this.form,
          port: this.form.port, // 保持字符串格式，支持多端口
          ip_list: this.form.ip_list, // 保持数组格式
          rap_tag_infos: (this.form.rap_tag_infos || []).join(","),
          operator: operator, // 添加操作人字段
        };

        let response;
        if (this.isEdit) {
          // 修改
          response = await http.updateHostGroupPlan(submitData);
        } else {
          // 新增
          response = await http.addHostGroupPlan(submitData);
        }

        if (response && response.code === 100000) {
          this.$message.success(this.isEdit ? "修改成功" : "新增成功");
          this.$emit("refresh");
          this.$emit("close");
        } else {
          this.$message.error(response?.message || (this.isEdit ? "修改失败" : "新增失败"));
        }
      } catch (error) {
        console.error("提交主机组规划失败:", error);
        this.$message.error(this.isEdit ? "修改失败" : "新增失败");
      } finally {
        this.submitting = false;
      }
    },
  },
};
</script>

<style scoped>
.dialog-footer {
  text-align: right;
}

.el-tag {
  margin-right: 10px;
  margin-bottom: 5px;
}

.error-field {
  border-color: #f56c6c;
}

/* 标签信息显示样式 */
.tag-info-container {
  max-height: 60px;
  overflow-y: auto;
}

.tag-info-container .el-tag {
  margin: 2px;
  max-width: 200px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  display: inline-block;
}

/* 标签提示框样式 */
.el-tooltip__popper .tag-tooltip-content {
  max-width: 300px;
  word-wrap: break-word;
}

/* 已添加IP的样式 */
.ip-already-added {
  color: #67c23a;
  font-weight: bold;
}

/* 表格行样式 - 已添加IP的行 */
.el-table__row .ip-already-added {
  opacity: 0.7;
}
</style>
